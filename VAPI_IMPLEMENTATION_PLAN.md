# Vapi Implementation Plan - Junior Dev Ready

## 🎯 Overview

**UPDATED:** Added Phase 0 for User ID & Thread ID foundation (required first!)

Implement dual-channel Vapi integration:
- **Phase 0**: User ID generation + localStorage + ChatContext integration
- **Channel 1**: Real-time function calls during conversation → Redis
- **Channel 2**: End-of-call webhook with transcript → Redis  
- **Frontend**: Periodic sync from Redis → localStorage → UI notifications

### 🔍 Current State Analysis:
- ✅ **Thread IDs**: Already working (generated by `generateId()` in ChatContext)
- ❌ **User IDs**: Added in Phase 0 - localStorage-based, no auth required
- ✅ **ID Storage**: localStorage pattern already established

## 📊 Data Mapping

### Vapi Webhook Payload → Current Transcript Format

**Input (Vapi webhook):**
```typescript
{
  id: "call_123",
  messages: [
    { role: "assistant", message: "Hey there. How can I help?", time: 1234567890 },
    { role: "user", message: "I need help with...", time: 1234567891 }
  ],
  metadata: { userId: "user_456", threadId: "thread_789" },
  analysis: { summary: "User called for help..." },
  startedAt: "2025-03-14T18:20:05.564Z",
  endedAt: "2025-03-14T18:20:37.233Z",
  endedReason: "customer-ended-call",
  artifactPlan: { recordingPath: "https://..." }
}
```

**Output (Current Transcript format):**
```typescript
{
  id: "transcript_call_123",
  summary: "User called for help...",
  transcript: "AI: Hey there. How can I help?\nUser: I need help with...\n",
  startedAt: "2025-03-14T18:20:05.564Z",
  endedAt: "2025-03-14T18:20:37.233Z", 
  endedReason: "customer-ended-call",
  recordingUrls: {
    mono: "https://...",
    stereo: "https://..."
  }
}
```

## 🏗️ Implementation Checklist

### Phase 0: User ID & Thread ID Foundation (REQUIRED FIRST)

- [ ] **0.1** Add User ID management to utils
  **File:** `src/lib/utils.ts`
  ```typescript
  import { type ClassValue, clsx } from "clsx"
  import { twMerge } from "tailwind-merge"
  
  export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
  }
  
  export function generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
  
  export function getCurrentTimestamp(): number {
    return Date.now();
  }
  
  // NEW: User ID management functions
  const USER_ID_KEY = 'app_user_id';
  
  export function generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  export function getUserId(): string {
    // Check if we're in browser environment
    if (typeof window === 'undefined') {
      return 'user_server'; // Fallback for server-side
    }
    
    try {
      // Get existing user ID from localStorage
      let userId = localStorage.getItem(USER_ID_KEY);
      
      if (!userId) {
        // Generate new user ID on first visit
        userId = generateUserId();
        localStorage.setItem(USER_ID_KEY, userId);
        console.log('Generated new user ID:', userId);
      }
      
      return userId;
    } catch (error) {
      console.error('Error managing user ID:', error);
      return `user_fallback_${Date.now()}`;
    }
  }
  
  export function resetUserId(): string {
    // For testing/demo purposes - generate new user ID
    const newUserId = generateUserId();
    try {
      localStorage.setItem(USER_ID_KEY, newUserId);
      console.log('Reset to new user ID:', newUserId);
    } catch (error) {
      console.error('Error resetting user ID:', error);
    }
    return newUserId;
  }
  ```

- [ ] **0.2** Add User ID to ChatContext
  **File:** `src/contexts/ChatContext.tsx`
  
  **Add to imports:**
  ```typescript
  import { generateId, getCurrentTimestamp, getUserId } from '@/lib/utils';
  ```
  
  **Add to ChatContextType interface:**
  ```typescript
  interface ChatContextType {
    threads: Thread[];
    currentThreadId: string | null;
    currentThread: Thread | null;
    userId: string; // NEW: Add user ID
    isLoading: boolean;
    // ... rest of existing interface
  }
  ```
  
  **Add to ChatProvider state:**
  ```typescript
  export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [threads, setThreads] = useState<Thread[]>([]);
    const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);
    const [userId] = useState<string>(getUserId()); // NEW: Initialize user ID
    const [isLoading, setIsLoading] = useState(true);
    // ... rest of existing state
  ```
  
  **Add to context provider value:**
  ```typescript
  return (
    <ChatContext.Provider
      value={{
        threads,
        currentThreadId,
        currentThread,
        userId, // NEW: Expose user ID
        isLoading,
        // ... rest of existing values
      }}
    >
      {children}
    </ChatContext.Provider>
  );
  ```

- [ ] **0.3** Update Redis utility to use real user IDs
  **File:** `src/lib/redis.ts`
  ```typescript
  import { Redis } from '@upstash/redis'
  import { getUserId, generateId } from './utils'
  
  export const redis = Redis.fromEnv()
  
  // Helper function to generate unique IDs
  export function generateItemId(type: string): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // Get current user ID (now uses real implementation)
  export function getCurrentUserId(): string {
    return getUserId()
  }
  ```

- [ ] **0.4** Test User ID generation
  **Create test component (temporary):**
  ```typescript
  // Add this to any page temporarily for testing
  import { getUserId, resetUserId } from '@/lib/utils';
  
  function TestUserID() {
    const handleReset = () => {
      const newId = resetUserId();
      window.location.reload(); // Reload to see new ID
    };
    
    return (
      <div style={{ position: 'fixed', top: 10, right: 10, background: 'black', color: 'white', padding: '10px' }}>
        <div>User ID: {getUserId()}</div>
        <button onClick={handleReset}>Reset User ID</button>
      </div>
    );
  }
  ```

### Phase 1: Setup & Dependencies

- [ ] **1.1** Install Upstash Redis dependency
  ```bash
  npm install @upstash/redis
  ```

- [ ] **1.2** Add environment variables to `.env.local`
  ```env
  UPSTASH_REDIS_REST_URL=your_url
  UPSTASH_REDIS_REST_TOKEN=your_token
  ```

- [ ] **1.3** Create Redis client utility
  **File:** `src/lib/redis.ts`
  ```typescript
  import { Redis } from '@upstash/redis'
  
  export const redis = Redis.fromEnv()
  
  // Helper function to generate unique IDs
  export function generateItemId(type: string): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // Helper to get user ID (now implemented!)
  export function getCurrentUserId(): string {
    return getUserId() // Uses the real implementation from utils
  }
  ```

### Phase 2: Create New Function Endpoints

- [ ] **2.1** Enhance existing appointment function
  **File:** `src/app/api/vapi/schedule-appointment/route.ts`
  ```typescript
  import { NextRequest, NextResponse } from 'next/server';
  import { redis, generateItemId, getCurrentUserId } from '@/lib/redis';
  
  export async function POST(request: NextRequest) {
    try {
      const body = await request.json();
      console.log('Appointment function call received:', body);
      
      // Extract function arguments and metadata
      const { 
        type, provider, specialty, date, time 
      } = body.message?.function?.arguments || body;
      
      // Get metadata from Vapi call (userId, threadId)
      const { userId, threadId } = body.call?.metadata || {};
      
      if (!userId || !threadId) {
        console.warn('Missing userId or threadId in appointment function call');
      }
      
      // Create appointment data
      const appointmentId = generateItemId('appointment');
      const appointmentData = {
        id: appointmentId,
        type: 'appointment',
        userId,
        threadId,
        data: {
          type: type || 'telehealth',
          provider: provider || 'Unknown Provider',
          specialty: specialty || 'General',
          date: date || new Date().toISOString().split('T')[0],
          time: time || '12:00'
        },
        createdAt: new Date().toISOString()
      };
      
      // Store in Redis
      await redis.setex(`pending_item:${appointmentId}`, 86400, JSON.stringify(appointmentData));
      
      // Add to user's pending list
      if (userId) {
        await redis.sadd(`user_pending_items:${userId}`, appointmentId);
      }
      
      return NextResponse.json({
        success: true,
        message: "Appointment scheduled successfully! I've added it to your focus section."
      });
      
    } catch (error) {
      console.error('Error processing appointment:', error);
      return NextResponse.json({
        success: true,
        message: "Appointment scheduled successfully!"
      });
    }
  }
  ```

- [ ] **2.2** Create new chat message function
  **File:** `src/app/api/vapi/send-message/route.ts`
  ```typescript
  import { NextRequest, NextResponse } from 'next/server';
  import { redis, generateItemId, getCurrentUserId } from '@/lib/redis';
  
  export async function POST(request: NextRequest) {
    try {
      const body = await request.json();
      console.log('Chat message function call received:', body);
      
      // Extract function arguments
      const { message, type } = body.message?.function?.arguments || body;
      
      // Get metadata from Vapi call
      const { userId, threadId } = body.call?.metadata || {};
      
      if (!userId || !threadId) {
        console.warn('Missing userId or threadId in message function call');
      }
      
      // Create message data
      const messageId = generateItemId('message');
      const messageData = {
        id: messageId,
        type: 'message',
        userId,
        threadId,
        data: {
          content: message,
          sender: 'ai',
          messageType: type || 'regular',
          fromCall: true
        },
        createdAt: new Date().toISOString()
      };
      
      // Store in Redis
      await redis.setex(`pending_item:${messageId}`, 86400, JSON.stringify(messageData));
      
      // Add to user's pending list
      if (userId) {
        await redis.sadd(`user_pending_items:${userId}`, messageId);
      }
      
      return NextResponse.json({
        success: true,
        message: "Message sent to chat successfully!"
      });
      
    } catch (error) {
      console.error('Error processing message:', error);
      return NextResponse.json({
        success: true,
        message: "Message sent successfully!"
      });
    }
  }
  ```

### Phase 3: Create Webhook Endpoint

- [ ] **3.1** Create Vapi webhook handler
  **File:** `src/app/api/vapi/webhook/route.ts`
  ```typescript
  import { NextRequest, NextResponse } from 'next/server';
  import { redis, generateItemId } from '@/lib/redis';
  
  export async function POST(request: NextRequest) {
    try {
      const payload = await request.json();
      console.log('Vapi webhook received:', payload.type, payload.id);
      
      // Only process end-of-call events
      if (payload.status !== 'ended') {
        return NextResponse.json({ received: true });
      }
      
      await handleCallCompletion(payload);
      return NextResponse.json({ received: true });
      
    } catch (error) {
      console.error('Webhook error:', error);
      return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
    }
  }
  
  async function handleCallCompletion(payload: any) {
    const { id: callId, metadata, messages, analysis, startedAt, endedAt, endedReason } = payload;
    
    // Extract user identification
    const { userId, threadId } = metadata || {};
    if (!userId) {
      console.warn('No userId in webhook metadata, skipping transcript storage');
      return;
    }
    
    // Convert Vapi messages to transcript format
    const transcriptText = messages?.map((msg: any) => {
      const role = msg.role === 'assistant' ? 'AI' : 'User';
      return `${role}: ${msg.message}`;
    }).join('\n') || '';
    
    // Handle recording URLs (may need adjustment based on actual Vapi response)
    const recordingUrls = {
      mono: payload.artifactPlan?.recordingPath || payload.recordingUrls?.mono || '',
      stereo: payload.recordingUrls?.stereo || payload.artifactPlan?.recordingPath || ''
    };
    
    // Create transcript data in existing format
    const transcriptId = generateItemId('transcript');
    const transcriptData = {
      id: transcriptId,
      type: 'transcript',
      userId,
      threadId,
      data: {
        id: `transcript_${callId}`,
        summary: analysis?.summary || 'Call completed',
        transcript: transcriptText,
        startedAt: startedAt,
        endedAt: endedAt,
        endedReason: endedReason || 'call-ended',
        recordingUrls: recordingUrls
      },
      createdAt: new Date().toISOString()
    };
    
    // Store in Redis
    await redis.setex(`pending_item:${transcriptId}`, 86400, JSON.stringify(transcriptData));
    
    // Add to user's pending list
    await redis.sadd(`user_pending_items:${userId}`, transcriptId);
    
    console.log(`Stored transcript for call ${callId}, user ${userId}`);
  }
  ```

### Phase 4: Create Sync API

- [ ] **4.1** Create pending items sync endpoint
  **File:** `src/app/api/sync/pending/route.ts`
  ```typescript
  import { NextRequest, NextResponse } from 'next/server';
  import { redis, getCurrentUserId } from '@/lib/redis';
  
  export async function GET(request: NextRequest) {
    try {
      const userId = getCurrentUserId(); // Uses real implementation
      
      // Get all pending item IDs for user
      const pendingIds = await redis.smembers(`user_pending_items:${userId}`) || [];
      
      if (pendingIds.length === 0) {
        return NextResponse.json({ items: [] });
      }
      
      // Fetch all pending items
      const items = [];
      for (const id of pendingIds) {
        const item = await redis.get(`pending_item:${id}`);
        if (item) {
          items.push(JSON.parse(item as string));
        }
      }
      
      return NextResponse.json({ items });
      
    } catch (error) {
      console.error('Sync error:', error);
      return NextResponse.json({ error: 'Sync failed' }, { status: 500 });
    }
  }
  
  export async function POST(request: NextRequest) {
    try {
      const { itemIds } = await request.json();
      const userId = getCurrentUserId();
      
      // Mark items as processed by removing them
      for (const id of itemIds) {
        await redis.srem(`user_pending_items:${userId}`, id);
        await redis.del(`pending_item:${id}`);
      }
      
      return NextResponse.json({ success: true });
      
    } catch (error) {
      console.error('Mark delivered error:', error);
      return NextResponse.json({ error: 'Failed to mark as delivered' }, { status: 500 });
    }
  }
  ```

### Phase 5: Add Metadata to Vapi Calls

- [ ] **5.1** Update VoiceAssistantModal for web calls
  **File:** `src/components/chat/VoiceAssistantModal.tsx`
  
  **Add to the `startSession` function around line 222:**
  ```typescript
  // Add this import at top of file
  import { useChat } from '@/contexts/ChatContext';
  
  // Add this inside the component
  const { userId } = useChat(); // Get userId from ChatContext
  
  // Add this before the vapi.start() call
  const currentThreadId = threadId || 'default_thread';
  
  await vapi.start({
    transcriber: {
      provider: "deepgram",
      model: "nova-2", 
      language: "en-US",
    },
    // ... existing config ...
    metadata: {
      userId: userId,
      threadId: currentThreadId
    },
    model: {
      provider: "openai",
      model: "gpt-4o-mini",
      messages: [
        // ... existing messages ...
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "scheduleAppointment",
            description: "Schedule a new appointment for the user and add it to their focus section",
            parameters: {
              // ... existing parameters ...
            }
          },
          server: {
            url: "https://gennext.vercel.app/api/vapi/schedule-appointment"
          }
        },
        // Add new chat message function
        {
          type: "function", 
          function: {
            name: "sendChatMessage",
            description: "Send a message to the user's chat thread",
            parameters: {
              type: "object",
              properties: {
                message: {
                  type: "string",
                  description: "The message to send to chat"
                },
                type: {
                  type: "string",
                  enum: ["regular", "summary", "follow-up"],
                  description: "Type of message"
                }
              },
              required: ["message"]
            }
          },
          server: {
            url: "https://gennext.vercel.app/api/vapi/send-message"
          }
        }
      ]
    },
    // ... rest of existing config ...
  });
  ```

- [ ] **5.2** Update outbound call API
  **File:** `src/app/api/outbound-call/route.ts`
  
  **Update the request body around line 128:**
  ```typescript
  // Add userId and threadId parameters to the API
  export async function POST(request: NextRequest) {
    try {
      const { phoneNumber, userId, threadId } = await request.json();
      
      // ... existing validation ...
      
      // Update the body to include metadata
      body: JSON.stringify({
        assistant: {
          ...assistantConfig,
          metadata: {
            userId: userId || 'unknown_user',
            threadId: threadId || 'unknown_thread'
          }
        },
        phoneNumberId: phoneNumberId,
        customer: {
          number: phoneNumber,
          customerId: userId
        },
        // Add webhook URL for end-of-call events
        serverUrl: "https://gennext.vercel.app/api/vapi/webhook"
      }),
      
      // ... rest of existing code ...
    }
  }
  ```

### Phase 6: Enhance Frontend Sync

- [ ] **6.1** Add sync functionality to ChatContext
  **File:** `src/contexts/ChatContext.tsx`
  
  **Add these functions to the ChatProvider:**
  ```typescript
  // Add after existing useEffect hooks
  const syncPendingItems = useCallback(async () => {
    try {
      const response = await fetch('/api/sync/pending');
      if (!response.ok) return;
      
      const { items } = await response.json();
      
      if (items.length === 0) return;
      
      let transcriptCount = 0;
      let appointmentCount = 0; 
      let messageCount = 0;
      
      // Process each item type
      items.forEach(item => {
        switch (item.type) {
          case 'transcript':
            // Add to transcripts (you may need to update useTranscripts hook)
            handleNewTranscript(item.data);
            transcriptCount++;
            break;
            
          case 'appointment':
            // Add to appointments (integration with focus system)
            handleNewAppointment(item.data);
            appointmentCount++;
            break;
            
          case 'message':
            // Add to chat thread
            if (item.threadId && item.data) {
              addMessageToThread(item.threadId, item.data);
            }
            messageCount++;
            break;
        }
      });
      
      // Show notifications
      if (transcriptCount > 0) {
        toast.success(`${transcriptCount} new call transcript(s) available`);
      }
      if (appointmentCount > 0) {
        toast.success(`${appointmentCount} appointment(s) added to focus`);
      }
      if (messageCount > 0) {
        toast.success(`${messageCount} new message(s) from call`);
      }
      
      // Mark items as processed
      await fetch('/api/sync/pending', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          itemIds: items.map(item => item.id) 
        })
      });
      
    } catch (error) {
      console.error('Failed to sync pending items:', error);
    }
  }, []);
  
  // Add helper functions
  const handleNewTranscript = (transcriptData: any) => {
    // Store transcript in localStorage (integrate with useTranscripts)
    try {
      const existingTranscripts = JSON.parse(localStorage.getItem('transcripts') || '[]');
      const updatedTranscripts = [transcriptData, ...existingTranscripts];
      localStorage.setItem('transcripts', JSON.stringify(updatedTranscripts));
    } catch (error) {
      console.error('Failed to store transcript:', error);
    }
  };
  
  const handleNewAppointment = (appointmentData: any) => {
    // Integration with focus system - you'll need to implement based on existing system
    console.log('New appointment to add to focus:', appointmentData);
  };
  
  const addMessageToThread = (threadId: string, messageData: any) => {
    const newMessage: Message = {
      id: generateId(),
      content: messageData.content,
      sender: messageData.sender,
      timestamp: Date.now(),
      messageType: messageData.messageType || 'regular'
    };
    
    setThreads(prevThreads =>
      prevThreads.map(thread =>
        thread.id === threadId
          ? { ...thread, messages: [...thread.messages, newMessage], lastModifiedAt: getCurrentTimestamp() }
          : thread
      )
    );
  };
  
  // Add periodic sync
  useEffect(() => {
    // Sync every 60 seconds and on window focus
    const interval = setInterval(syncPendingItems, 60000);
    
    const handleFocus = () => {
      syncPendingItems();
    };
    
    window.addEventListener('focus', handleFocus);
    
    // Initial sync
    syncPendingItems();
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, [syncPendingItems]);
  ```

### Phase 7: Configure Webhook URL

- [ ] **7.1** Set webhook URL in Vapi Dashboard
  - Go to Vapi Dashboard → Settings → Webhooks
  - Add webhook URL: `https://gennext.vercel.app/api/vapi/webhook`
  - Enable for call completion events

### Phase 8: Testing & Validation

- [ ] **8.1** Test function calls during conversation
  - Start a call and ask assistant to schedule appointment
  - Check Redis for stored appointment data
  - Verify frontend sync picks up the data

- [ ] **8.2** Test end-of-call webhook
  - Complete a call
  - Check webhook receives payload
  - Verify transcript stored in Redis
  - Confirm frontend sync displays transcript

- [ ] **8.3** Test metadata passing
  - Verify userId and threadId appear in function call payloads
  - Confirm metadata appears in webhook payloads

## 🚨 Notes & Considerations

### Foundation Requirements
- **CRITICAL**: Phase 0 must be completed first - all Vapi integration depends on user ID management
- Thread IDs are already working (generated by `generateId()` in ChatContext)
- User IDs are now implemented with localStorage persistence

### Error Handling
- All endpoints include try/catch blocks
- Failed items remain in Redis for retry
- Frontend sync is resilient to API failures

### Data Expiration
- Redis items expire after 24 hours
- Prevents storage bloat from missed syncs

### Recording URLs
- Recording URL structure may vary in actual Vapi responses
- Test and adjust webhook mapping as needed

### Notifications
- Uses existing toast system
- Can be enhanced with more detailed notifications

## 🎯 Success Criteria

✅ **When complete, you should have:**
- Appointments scheduled during calls appear in focus section
- Chat messages sent during calls appear in chat threads
- Call transcripts automatically appear in transcript list
- User receives notifications for new content
- All data properly associated with correct users and threads

**Estimated Implementation Time:** 4-6 hours for experienced dev, 8-12 hours for junior dev