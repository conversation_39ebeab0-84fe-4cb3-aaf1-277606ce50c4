import { Redis } from '@upstash/redis'
import { getUserId, generateId } from './utils'

export const redis = Redis.fromEnv()

// Helper function to generate unique IDs
export function generateItemId(type: string): string {
  return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Get current user ID (now uses real implementation)
export function getCurrentUserId(): string {
  return getUserId()
}
