"use client";

import React, { useContext, createContext, useState, useEffect, useCallback } from 'react';
import { Thread, Message, ChatResponsePart, ForYouItem } from '@/types'; // Import ForYouItem
import { generateId, getCurrentTimestamp, getUserId } from '@/lib/utils';
import { toast } from 'sonner';

interface ChatContextType {
  threads: Thread[];
  currentThreadId: string | null;
  currentThread: Thread | null;
  userId: string; // NEW: Add user ID
  isLoading: boolean;
  isSending: boolean;
  setIsSending: (sending: boolean) => void;
  createThread: (topicOptions?: { name: string; initialMessage: string }) => string;
  selectThread: (threadId: string) => void;
  renameThread: (threadId: string, newName: string) => void;
  deleteThread: (threadId: string) => void;
  addMessage: (content: string | ChatResponsePart[], sender: 'user' | 'ai') => void;
  addVoiceCallEvent: (eventType: 'start' | 'end', targetThreadId?: string) => void; // New: Function to add voice call events
  markInitialMessageAsSent: (threadId: string) => void;
  allAvailableForYouItems: ForYouItem[]; // New: For dynamic For You items
  addForYouItems: (items: ForYouItem[]) => void; // New: Function to add new For You items
  selectedTranscriptIds: string[];
  addTranscript: (id: string) => void;
  removeTranscript: (id: string) => void;
  clearSelectedTranscripts: () => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

const LOCAL_STORAGE_KEY_THREADS = 'chatThreads';
const LOCAL_STORAGE_KEY_FOR_YOU_ITEMS = 'allAvailableForYouItems'; // New key for For You items

export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [threads, setThreads] = useState<Thread[]>([]);
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);
  const [userId] = useState<string>(getUserId()); // NEW: Initialize user ID
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [allAvailableForYouItems, setAllAvailableForYouItems] = useState<ForYouItem[]>([]); // New state
  const [selectedTranscriptIds, setSelectedTranscriptIds] = useState<string[]>([]);

  // Initial For You items: As per request, this will be empty by default and populated by AI.
  // We keep it as an empty array to ensure proper initialization if nothing is in local storage.
  const defaultForYouItems: ForYouItem[] = [];

  useEffect(() => {
    try {
      const storedThreads = localStorage.getItem(LOCAL_STORAGE_KEY_THREADS);
      if (storedThreads) {
        const parsedThreads = JSON.parse(storedThreads) as Thread[];
        setThreads(parsedThreads);
        if (parsedThreads.length > 0) {
          // Select the most recently modified thread
          const sortedThreads = [...parsedThreads].sort((a, b) => b.lastModifiedAt - a.lastModifiedAt);
          setCurrentThreadId(sortedThreads[0].id);
        }
      }

      // Load For You items from local storage
      const storedForYouItems = localStorage.getItem(LOCAL_STORAGE_KEY_FOR_YOU_ITEMS);
      if (storedForYouItems) {
        const parsedForYouItems = JSON.parse(storedForYouItems) as ForYouItem[];
        setAllAvailableForYouItems(parsedForYouItems);
      } else {
        setAllAvailableForYouItems(defaultForYouItems); // Initialize with default (empty) if nothing in storage
      }

    } catch (error) {
      console.error("Failed to load data from localStorage", error);
      toast.error("Could not load your chat history or personalized suggestions.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!isLoading) { // Only save if not initial loading
      try {
        localStorage.setItem(LOCAL_STORAGE_KEY_THREADS, JSON.stringify(threads));
        localStorage.setItem(LOCAL_STORAGE_KEY_FOR_YOU_ITEMS, JSON.stringify(allAvailableForYouItems)); // Save For You items
      } catch (error) {
        console.error("Failed to save data to localStorage", error);
        toast.error("Could not save your chat history or personalized suggestions.");
      }
    }
  }, [threads, allAvailableForYouItems, isLoading]); // Add allAvailableForYouItems to dependencies

  const createThread = useCallback((topicOptions?: { name: string; initialMessage: string }): string => {
    const newThreadId = generateId();
    let newThread: Thread;

    if (topicOptions) {
      const initialMessage: Message = {
        id: generateId(),
        sender: 'user',
        content: topicOptions.initialMessage,
        timestamp: getCurrentTimestamp(),
      };
      newThread = {
        id: newThreadId,
        name: topicOptions.name,
        messages: [initialMessage],
        createdAt: getCurrentTimestamp(),
        lastModifiedAt: getCurrentTimestamp(),
        initialMessageSent: false,
      };
    } else {
      newThread = {
        id: newThreadId,
        name: `New Chat ${threads.length + 1}`,
        messages: [],
        createdAt: getCurrentTimestamp(),
        lastModifiedAt: getCurrentTimestamp(),
        initialMessageSent: true,
      };
    }

    setThreads(prevThreads => [newThread, ...prevThreads]);
    setCurrentThreadId(newThreadId);
    toast.success("New chat created!");
    return newThreadId;
  }, [threads]);

  const selectThread = useCallback((threadId: string) => {
    const threadExists = threads.find(t => t.id === threadId);
    if (threadExists) {
      setCurrentThreadId(threadId);
    } else {
      toast.error("Thread not found.");
    }
  }, [threads]);

  const renameThread = useCallback((threadId: string, newName: string) => {
    setThreads(prevThreads =>
      prevThreads.map(thread =>
        thread.id === threadId ? { ...thread, name: newName, lastModifiedAt: getCurrentTimestamp() } : thread
      )
    );
    toast.success(`Chat renamed to "${newName}"`);
  }, []);

  const deleteThread = useCallback((threadId: string) => {
    setThreads(prevThreads => prevThreads.filter(thread => thread.id !== threadId));
    if (currentThreadId === threadId) {
      setCurrentThreadId(threads.length > 1 ? threads[0].id : null);
    }
    toast.success("Chat deleted.");
  }, [currentThreadId, threads]);

  const markInitialMessageAsSent = useCallback((threadId: string) => {
    setThreads(prevThreads =>
      prevThreads.map(thread =>
        thread.id === threadId ? { ...thread, initialMessageSent: true } : thread
      )
    );
  }, []);

  const addMessage = useCallback((content: string | ChatResponsePart[], sender: 'user' | 'ai' | 'voice_assistant') => {
    if (!currentThreadId) {
      toast.error("No active chat selected.");
      return;
    }

    const newMessage: Message = {
      id: generateId(),
      content,
      sender,
      timestamp: getCurrentTimestamp(),
      messageType: 'regular',
    };

    setThreads(prevThreads =>
      prevThreads.map(thread =>
        thread.id === currentThreadId
          ? { ...thread, messages: [...thread.messages, newMessage], lastModifiedAt: getCurrentTimestamp() }
          : thread
      )
    );
  }, [currentThreadId]);

  const addVoiceCallEvent = useCallback((eventType: 'start' | 'end', targetThreadId?: string) => {
    const threadToUpdate = targetThreadId || currentThreadId;
    if (!threadToUpdate) {
      return; // Silently fail if no thread is specified or active
    }

    const eventMessage: Message = {
      id: generateId(),
      content: eventType === 'start' ? 'Voice call started' : 'Voice call ended',
      sender: 'system',
      timestamp: getCurrentTimestamp(),
      messageType: eventType === 'start' ? 'voice_call_start' : 'voice_call_end',
    };

    setThreads(prevThreads =>
      prevThreads.map(thread =>
        thread.id === threadToUpdate
          ? { ...thread, messages: [...thread.messages, eventMessage], lastModifiedAt: getCurrentTimestamp() }
          : thread
      )
    );
  }, [currentThreadId]);

  const addTranscript = useCallback((id: string) => {
    setSelectedTranscriptIds((prev) => {
      if (prev.includes(id)) return prev;
      return [...prev, id];
    });
  }, []);

  const removeTranscript = useCallback((id: string) => {
    setSelectedTranscriptIds((prev) => prev.filter((transcriptId) => transcriptId !== id));
  }, []);

  const clearSelectedTranscripts = useCallback(() => {
    setSelectedTranscriptIds([]);
  }, []);

  // New function to add For You items to the overall list
  const addForYouItems = useCallback((items: ForYouItem[]) => {
    setAllAvailableForYouItems(prevItems => {
      // Filter out duplicates based on id to prevent adding the same item multiple times
      const newItems = items.filter(newItem => !prevItems.some(existingItem => existingItem.id === newItem.id));
      return [...prevItems, ...newItems];
    });
    // The useEffect will handle saving to localStorage due to its dependency on allAvailableForYouItems
  }, []);

  // Sync functionality for Vapi integration
  const syncPendingItems = useCallback(async () => {
    try {
      console.log('FRONTEND SYNC: Starting sync for userId:', userId);
      const response = await fetch(`/api/sync/pending?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) {
        console.log('FRONTEND SYNC: Response not ok:', response.status);
        return;
      }

      const { items } = await response.json();
      console.log('FRONTEND SYNC: Received items:', items.length, items);

      if (items.length === 0) {
        console.log('FRONTEND SYNC: No items to process');
        return;
      }

      let transcriptCount = 0;
      let appointmentCount = 0;
      let messageCount = 0;

      // Process each item type
      items.forEach((item: any) => {
        console.log('FRONTEND SYNC: Processing item:', item.type, item.id);
        switch (item.type) {
          case 'transcript':
            console.log('FRONTEND SYNC: Processing transcript:', item.data);
            handleNewTranscript(item.data);
            transcriptCount++;
            break;

          case 'appointment':
            console.log('FRONTEND SYNC: Processing appointment:', item.data);
            handleNewAppointment(item.data);
            appointmentCount++;
            break;

          case 'message':
            console.log('FRONTEND SYNC: Processing message for thread:', item.threadId, item.data);
            if (item.threadId && item.data) {
              addMessageToThread(item.threadId, item.data);
            }
            messageCount++;
            break;
        }
      });

      // Show notifications
      if (transcriptCount > 0) {
        toast.success(`${transcriptCount} new call transcript(s) available`);
      }
      if (appointmentCount > 0) {
        toast.success(`${appointmentCount} appointment(s) added to focus`);
      }
      if (messageCount > 0) {
        toast.success(`${messageCount} new message(s) from call`);
      }

      // Mark items as processed
      await fetch('/api/sync/pending', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          itemIds: items.map((item: any) => item.id)
        })
      });

    } catch (error) {
      console.error('Failed to sync pending items:', error);
    }
  }, []);

  // Helper functions for sync
  const handleNewTranscript = (transcriptData: any) => {
    console.log('TRANSCRIPT HANDLER: Storing transcript:', transcriptData);
    // Store transcript in localStorage (integrate with useTranscripts)
    try {
      const existingTranscripts = JSON.parse(localStorage.getItem('transcripts') || '[]');
      console.log('TRANSCRIPT HANDLER: Existing transcripts count:', existingTranscripts.length);
      const updatedTranscripts = [transcriptData, ...existingTranscripts];
      localStorage.setItem('transcripts', JSON.stringify(updatedTranscripts));
      console.log('TRANSCRIPT HANDLER: Stored transcript, new count:', updatedTranscripts.length);
    } catch (error) {
      console.error('TRANSCRIPT HANDLER: Failed to store transcript:', error);
    }
  };

  const handleNewAppointment = (appointmentData: any) => {
    console.log('APPOINTMENT HANDLER: Processing appointment:', appointmentData);
    // Integration with focus system - you'll need to implement based on existing system
    console.log('New appointment to add to focus:', appointmentData);
  };

  const addMessageToThread = (threadId: string, messageData: any) => {
    console.log('MESSAGE HANDLER: Adding message to thread:', threadId, messageData);
    const newMessage: Message = {
      id: generateId(),
      content: messageData.content,
      sender: messageData.sender as 'user' | 'ai' | 'system' | 'voice_assistant',
      timestamp: Date.now(),
      messageType: messageData.messageType || 'regular'
    };

    console.log('MESSAGE HANDLER: Created message:', newMessage);
    setThreads(prevThreads => {
      const updated = prevThreads.map(thread =>
        thread.id === threadId
          ? { ...thread, messages: [...thread.messages, newMessage], lastModifiedAt: getCurrentTimestamp() }
          : thread
      );
      console.log('MESSAGE HANDLER: Updated threads, found target thread:', updated.some(t => t.id === threadId));
      return updated;
    });
  };

  // Add periodic sync
  useEffect(() => {
    // Sync every 60 seconds and on window focus
    const interval = setInterval(syncPendingItems, 60000);

    const handleFocus = () => {
      syncPendingItems();
    };

    window.addEventListener('focus', handleFocus);

    // Initial sync
    syncPendingItems();

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, [syncPendingItems]);

  const currentThread = threads.find(thread => thread.id === currentThreadId) || null;

  return (
    <ChatContext.Provider
      value={{
        threads,
        currentThreadId,
        currentThread,
        userId, // NEW: Expose user ID
        isLoading,
        isSending,
        setIsSending,
        createThread,
        selectThread,
        renameThread,
        deleteThread,
        addMessage,
        addVoiceCallEvent, // Expose the function to add voice call events
        markInitialMessageAsSent,
        allAvailableForYouItems, // Expose all available For You items
        addForYouItems, // Expose the function to add new For You items
        selectedTranscriptIds,
        addTranscript,
        removeTranscript,
        clearSelectedTranscripts,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};