import { NextRequest, NextResponse } from 'next/server';
import { redis, generateItemId, isRedisAvailable } from '@/lib/redis';

export async function POST(request: NextRequest) {
  try {
    const payload = await request.json();
    console.log('Vapi webhook received:', payload.type, payload.id);

    // Only process end-of-call events
    if (payload.status !== 'ended') {
      return NextResponse.json({ received: true });
    }

    await handleCallCompletion(payload);
    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handleCallCompletion(payload: any) {
  const { id: callId, metadata, messages, analysis, startedAt, endedAt, endedReason } = payload;

  // Extract user identification
  const { userId, threadId } = metadata || {};
  if (!userId) {
    console.warn('No userId in webhook metadata, skipping transcript storage');
    return;
  }

  // Only store in Redis if it's available
  if (!isRedisAvailable() || !redis) {
    console.warn('Redis not available, transcript data not stored');
    return;
  }

  // Convert Vapi messages to transcript format
  const transcriptText = messages?.map((msg: any) => {
    const role = msg.role === 'assistant' ? 'AI' : 'User';
    return `${role}: ${msg.message}`;
  }).join('\n') || '';

  // Handle recording URLs (may need adjustment based on actual Vapi response)
  const recordingUrls = {
    mono: payload.artifactPlan?.recordingPath || payload.recordingUrls?.mono || '',
    stereo: payload.recordingUrls?.stereo || payload.artifactPlan?.recordingPath || ''
  };

  // Create transcript data in existing format
  const transcriptId = generateItemId('transcript');
  const transcriptData = {
    id: transcriptId,
    type: 'transcript',
    userId,
    threadId,
    data: {
      id: `transcript_${callId}`,
      summary: analysis?.summary || 'Call completed',
      transcript: transcriptText,
      startedAt: startedAt,
      endedAt: endedAt,
      endedReason: endedReason || 'call-ended',
      recordingUrls: recordingUrls
    },
    createdAt: new Date().toISOString()
  };

  // Store in Redis
  await redis.setex(`pending_item:${transcriptId}`, 86400, JSON.stringify(transcriptData));

  // Add to user's pending list
  await redis.sadd(`user_pending_items:${userId}`, transcriptId);

  console.log(`Stored transcript for call ${callId}, user ${userId}`);
}
