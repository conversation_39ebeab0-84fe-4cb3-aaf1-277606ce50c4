import { NextRequest, NextResponse } from 'next/server';
import { redis, generateItemId, isRedisAvailable } from '@/lib/redis';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Chat message function call received:', body);
    console.log('Shape of data:', JSON.stringify(body, null, 2));

    // Extract function arguments from the correct VAPI structure
    const toolCall = body.message?.toolCalls?.[0];
    const functionArgs = toolCall?.function?.arguments || {};

    const { message, type } = functionArgs;

    // Get metadata from Vapi call
    const { userId, threadId } = body.message?.assistant?.metadata || {};
    console.log('VAPI SEND MESSAGE: Extracted metadata - userId:', userId, 'threadId:', threadId);

    if (!userId || !threadId) {
      console.warn('VAPI SEND MESSAGE: Missing userId or threadId in message function call');
      console.log('VAPI SEND MESSAGE: Available metadata paths:', {
        'body.message?.assistant?.metadata': body.message?.assistant?.metadata,
        'body.assistant?.metadata': body.assistant?.metadata,
        'body.metadata': body.metadata
      });
    }

    // Only store in Redis if it's available and we have required metadata
    if (isRedisAvailable() && redis && userId && threadId) {
      // Create message data
      const messageId = generateItemId('message');
      const messageData = {
        id: messageId,
        type: 'message',
        userId,
        threadId,
        data: {
          content: message,
          sender: 'voice_assistant',
          messageType: type || 'regular',
          fromCall: true
        },
        createdAt: new Date().toISOString()
      };

      // Store in Redis
      await redis.setex(`pending_item:${messageId}`, 86400, JSON.stringify(messageData));

      // Add to user's pending list
      if (userId) {
        await redis.sadd(`user_pending_items:${userId}`, messageId);
      }
      console.log('VAPI SEND MESSAGE: Successfully stored message with userId:', userId, 'threadId:', threadId);
    } else {
      console.warn('VAPI SEND MESSAGE: Skipping storage - Redis available:', isRedisAvailable(), 'userId:', userId, 'threadId:', threadId);
    }

    return NextResponse.json({
      success: true,
      message: "Message sent to chat successfully!"
    });

  } catch (error) {
    console.error('Error processing message:', error);
    return NextResponse.json({
      success: true,
      message: "Message sent successfully!"
    });
  }
}

export async function OPTIONS(_req: Request) {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
    });
}