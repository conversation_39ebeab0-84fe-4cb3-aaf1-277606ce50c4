import { NextRequest, NextResponse } from 'next/server';
import { redis, generateItemId } from '@/lib/redis';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Appointment function call received:', body);

    // Extract function arguments and metadata
    const {
      type, provider, specialty, date, time
    } = body.message?.function?.arguments || body;

    // Get metadata from Vapi call (userId, threadId)
    const { userId, threadId } = body.call?.metadata || {};

    if (!userId || !threadId) {
      console.warn('Missing userId or threadId in appointment function call');
    }

    // Create appointment data
    const appointmentId = generateItemId('appointment');
    const appointmentData = {
      id: appointmentId,
      type: 'appointment',
      userId,
      threadId,
      data: {
        type: type || 'telehealth',
        provider: provider || 'Unknown Provider',
        specialty: specialty || 'General',
        date: date || new Date().toISOString().split('T')[0],
        time: time || '12:00'
      },
      createdAt: new Date().toISOString()
    };

    // Store in Redis
    await redis.setex(`pending_item:${appointmentId}`, 86400, JSON.stringify(appointmentData));

    // Add to user's pending list
    if (userId) {
      await redis.sadd(`user_pending_items:${userId}`, appointmentId);
    }

    return NextResponse.json({
      success: true,
      message: "Appointment scheduled successfully! I've added it to your focus section."
    });

  } catch (error) {
    console.error('Error processing appointment:', error);
    return NextResponse.json({
      success: true,
      message: "Appointment scheduled successfully!"
    });
  }
}