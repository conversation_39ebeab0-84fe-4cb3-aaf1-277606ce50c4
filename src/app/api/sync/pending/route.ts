import { NextRequest, NextResponse } from 'next/server';
import { redis, getCurrentUserId } from '@/lib/redis';

export async function GET(request: NextRequest) {
  try {
    const userId = getCurrentUserId(); // Uses real implementation
    
    // Get all pending item IDs for user
    const pendingIds = await redis.smembers(`user_pending_items:${userId}`) || [];
    
    if (pendingIds.length === 0) {
      return NextResponse.json({ items: [] });
    }
    
    // Fetch all pending items
    const items = [];
    for (const id of pendingIds) {
      const item = await redis.get(`pending_item:${id}`);
      if (item) {
        items.push(JSON.parse(item as string));
      }
    }
    
    return NextResponse.json({ items });
    
  } catch (error) {
    console.error('Sync error:', error);
    return NextResponse.json({ error: 'Sync failed' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { itemIds } = await request.json();
    const userId = getCurrentUserId();
    
    // Mark items as processed by removing them
    for (const id of itemIds) {
      await redis.srem(`user_pending_items:${userId}`, id);
      await redis.del(`pending_item:${id}`);
    }
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Mark delivered error:', error);
    return NextResponse.json({ error: 'Failed to mark as delivered' }, { status: 500 });
  }
}
